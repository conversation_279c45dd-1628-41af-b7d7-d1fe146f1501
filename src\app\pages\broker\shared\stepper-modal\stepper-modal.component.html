<div class="mb-5 mt-0">
  <app-broker-title [showCreateButton]="false"></app-broker-title>
</div>

<div class="card rounded-4">
  <div class="card-body p-10">
    <div
      class="stepper stepper-pills d-flex flex-column"
      id="hotel_unit_rental_stepper"
    >
      <!-- Header and Progress Bar -->
      <div class="mb-5 text-center">
        <h2 class="fw-bold text-dark-blue mb-2">
          <ng-container *ngIf="currentStep === 1">Order Settings</ng-container>
          <ng-container *ngIf="currentStep === 2"
            >Location Information</ng-container
          >
          <ng-container *ngIf="currentStep === 3"
            >Unit Information</ng-container
          >
          <ng-container *ngIf="currentStep === 4"
            >Project Documents</ng-container
          >
          <ng-container *ngIf="currentStep === 5"
            >Financial Information</ng-container
          >
        </h2>

        <div class="d-flex justify-content-center align-items-center mb-2">
          <span class="text-success fw-bold">Step {{ currentStep }}</span>
          <span class="text-muted mx-1">of</span>
          <span class="text-muted">{{ totalSteps }}</span>
        </div>

        <div
          *ngIf="currentStep > 1"
          class="text-primary cursor-pointer mb-2"
          (click)="prevStep()"
        >
          Back to previous step
        </div>

        <div class="progress h-8px bg-light-success w-75 mx-auto">
          <div
            class="progress-bar bg-success"
            role="progressbar"
            [style.width]="(currentStep / totalSteps) * 100 + '%'"
            aria-valuenow="50"
            aria-valuemin="0"
            aria-valuemax="100"
          ></div>
        </div>
      </div>

      <!-- Form Content -->
      <form class="mx-auto w-100 pt-5 pb-10">
        <!-- Step 1: Basic Request Settings -->
        <div *ngIf="currentStep === 1" [formGroup]="step1Form">
          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block"
              >Specialization Scope</label
            >
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button"
                id="locationTypeDropdown"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <span>{{
                  getText(
                    specializationScope,
                    step1Form.get("locationType")?.value
                  ) || "Select Specialization Scope"
                }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul
                class="dropdown-menu w-100"
                aria-labelledby="locationTypeDropdown"
              >
                <li *ngFor="let scope of specializationScope">
                  <a
                    class="dropdown-item"
                    (click)="select(step1Form, 'locationType', scope.value)"
                    >{{ scope.key }}</a
                  >
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block"
              >Request Type</label
            >
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button"
                id="requestTypeDropdown"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <span>{{
                  getText(requestTypes, step1Form.get("requestType")?.value) ||
                    "Select Request Type"
                }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul
                class="dropdown-menu w-100"
                aria-labelledby="requestTypeDropdown"
              >
                <li *ngFor="let requestType of requestTypes">
                  <a
                    class="dropdown-item"
                    (click)="
                      select(step1Form, 'requestType', requestType.value)
                    "
                    >{{ requestType.key }}</a
                  >
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block"
              >Unit Type</label
            >
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button"
                id="unitTypeDropdown"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <span>{{
                  getText(unitTypeOptions, step1Form.get("unitType")?.value) ||
                    "Select Unit Type"
                }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul
                class="dropdown-menu w-100"
                aria-labelledby="unitTypeDropdown"
                style="
                  max-height: 200px;
                  overflow-y: auto;
                  position: absolute;
                  z-index: 1000;
                "
              >
                <li *ngFor="let unitType of unitTypeOptions">
                  <a
                    class="dropdown-item"
                    (click)="select(step1Form, 'unitType', unitType.value)"
                    >{{ unitType.key }}</a
                  >
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Step 2: Location Information -->
        <div *ngIf="currentStep === 2" [formGroup]="step2Form">
          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block"
              >Compound Name</label
            >
            <input
              type="text"
              class="form-control"
              formControlName="compoundName"
              placeholder="Enter Compound Name"
            />
          </div>
          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block"
              >Mall Name</label
            >
            <input
              type="text"
              class="form-control"
              formControlName="mallName"
              placeholder="Enter Mall Name"
            />
          </div>
          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">City</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button"
                id="cityDropdown"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <span>{{ selectedCityName || "Select City" }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul
                class="dropdown-menu w-100"
                aria-labelledby="cityDropdown"
                style="
                  max-height: 200px;
                  overflow-y: auto;
                  position: absolute;
                  z-index: 1000;
                "
              >
                <!-- Debug info -->
                <li class="dropdown-item disabled">
                  Total Cities: {{ cities.length }}
                </li>

                <ng-container
                  *ngIf="cities && cities.length > 0; else noCities"
                >
                  <li *ngFor="let city of cities" style="cursor: pointer">
                    <a
                      class="dropdown-item text-start"
                      (click)="selectCity(city.id, city.name_en)"
                    >
                      {{ city.name_en }}
                    </a>
                  </li>
                </ng-container>

                <ng-template #noCities>
                  <li>
                    <a class="dropdown-item text-start disabled">
                      No cities available
                    </a>
                  </li>
                </ng-template>
              </ul>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">Area</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button"
                id="areaDropdown"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <span>{{ selectedAreaName || "Select Area" }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul
                class="dropdown-menu w-100"
                aria-labelledby="areaDropdown"
                style="
                  max-height: 200px;
                  overflow-y: auto;
                  position: absolute;
                  z-index: 1000;
                "
              >
                <li *ngIf="areas.length > 0">
                  <a
                    *ngFor="let area of areas"
                    class="dropdown-item text-start"
                    (click)="selectArea(area.id, area.name_en)"
                    >{{ area.name_en }}</a
                  >
                </li>
                <li *ngIf="areas.length === 0">
                  <a class="dropdown-item text-start disabled">
                    No areas available
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block"
              >Sub Area</label
            >
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button"
                id="subAreaDropdown"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <span>{{ selectedSubAreaName || "Select Sub Area" }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul
                class="dropdown-menu w-100"
                aria-labelledby="subAreaDropdown"
                style="
                  max-height: 200px;
                  overflow-y: auto;
                  position: absolute;
                  z-index: 1000;
                "
              >
                <li *ngIf="subAreas.length > 0">
                  <a
                    *ngFor="let subArea of subAreas"
                    class="dropdown-item text-start"
                    (click)="selectSubArea(subArea.id, subArea.name_en)"
                    >{{ subArea.name_en }}</a
                  >
                </li>
                <li *ngIf="subAreas.length === 0">
                  <a class="dropdown-item text-start disabled">
                    No sub areas available
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">
              Village Name
            </label>
            <input
              type="text"
              class="form-control text-start"
              formControlName="villageName"
              placeholder="Enter village name"
            />
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">
              Detailed Address
            </label>
            <input
              type="text"
              class="form-control text-start"
              formControlName="detailedAddress"
              placeholder="Enter detailed address"
            />
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">
              Google Maps Link
            </label>
            <input
              type="text"
              class="form-control text-start"
              [ngClass]="{
                'is-invalid':
                  step2Form.get('location')?.invalid &&
                  (step2Form.get('location')?.touched ||
                    step2Form.get('location')?.dirty)
              }"
              formControlName="location"
              placeholder="Enter Google Maps link"
            />
            <div
              *ngIf="
                step2Form.get('location')?.invalid &&
                (step2Form.get('location')?.touched ||
                  step2Form.get('location')?.dirty)
              "
              class="invalid-feedback"
            >
              <div *ngIf="step2Form.get('location')?.errors?.['required']">
                Google Maps link is required.
              </div>
              <div *ngIf="step2Form.get('location')?.errors?.['pattern']">
                Please enter a valid URL (e.g., https://maps.google.com/...).
              </div>
            </div>
          </div>

          <div class="mb-10">
            <div class="form-check form-check-custom form-check-solid">
              <input
                class="form-check-input"
                type="checkbox"
                formControlName="locationSuggestion"
                id="locationSuggestion"
              />
              <label
                class="form-check-label text-start"
                for="locationSuggestion"
              >
                Location Suggestion
              </label>
            </div>
          </div>
        </div>

        <!-- Step 3: Unit Information -->
        <div *ngIf="currentStep === 3" [formGroup]="step3Form">
          <div class="row mb-10">
            <div class="col-md-6 mb-5">
              <label class="form-label fw-bold">Unit Number/Letter</label>
              <input
                type="text"
                class="form-control form-control-solid"
                formControlName="unitNumber"
              />
            </div>
            <div class="col-md-6 mb-5">
              <label class="form-label fw-bold">Building Number</label>
              <input
                type="text"
                class="form-control form-control-solid"
                formControlName="buildingNumber"
              />
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block"
              >Unit Type</label
            >
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button"
                id="unitTypeDropdown"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <span>{{
                  getText(unitTypes, step3Form.get("unitType")?.value) ||
                    "Select Unit Type"
                }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul
                class="dropdown-menu w-100"
                aria-labelledby="unitTypeDropdown"
                style="
                  max-height: 200px;
                  overflow-y: auto;
                  position: absolute;
                  z-index: 1000;
                "
              >
                <li *ngFor="let unitType of unitTypes">
                  <a
                    class="dropdown-item text-start"
                    (click)="select(step3Form, 'unitType', unitType.value)"
                    >{{ unitType.key }}</a
                  >
                </li>
                <li *ngIf="unitTypes.length === 0">
                  <a class="dropdown-item text-start disabled">
                    No unit types available
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <!-- Area Information Section -->
          <div class="mb-10">
            <label class="form-label fw-bold">Unit Area (Square Meters)</label>
            <input
              type="number"
              class="form-control form-control-solid"
              formControlName="unitArea"
              min="0"
            />
          </div>

          <div class="row mb-10">
            <div class="col-md-6 mb-5">
              <label class="form-label fw-bold"
                >Unit Area Min (Square Meters)</label
              >
              <input
                type="number"
                class="form-control form-control-solid"
                formControlName="unitAreaMin"
                min="0"
              />
            </div>
            <div class="col-md-6 mb-5">
              <label class="form-label fw-bold"
                >Unit Area Max (Square Meters)</label
              >
              <input
                type="number"
                class="form-control form-control-solid"
                formControlName="unitAreaMax"
                min="0"
              />
            </div>
          </div>

          <div class="mb-10">
            <div class="form-check form-check-custom form-check-solid">
              <input
                class="form-check-input"
                type="checkbox"
                formControlName="areaSuggestions"
                id="areaSuggestions"
              />
              <label class="form-check-label" for="areaSuggestions">
                Area suggestions
              </label>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold"
              >Building Area (Square Meters)</label
            >
            <input
              type="number"
              class="form-control form-control-solid"
              formControlName="buildingArea"
              min="0"
            />
          </div>

          <div class="row mb-10">
            <div class="col-md-6 mb-5">
              <label class="form-label fw-bold"
                >Building Area Min (Square Meters)</label
              >
              <input
                type="number"
                class="form-control form-control-solid"
                formControlName="buildingAreaMin"
                min="0"
              />
            </div>
            <div class="col-md-6 mb-5">
              <label class="form-label fw-bold"
                >Building Area Max (Square Meters)</label
              >
              <input
                type="number"
                class="form-control form-control-solid"
                formControlName="buildingAreaMax"
                min="0"
              />
            </div>
          </div>

          <div class="mb-10">
            <div class="form-check form-check-custom form-check-solid">
              <input
                class="form-check-input"
                type="checkbox"
                formControlName="averageBuildingAreaSuggestions"
                id="averageBuildingAreaSuggestions"
              />
              <label
                class="form-check-label"
                for="averageBuildingAreaSuggestions"
              >
                Average building area suggestions
              </label>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold"
              >Ground Area (Square Meters)</label
            >
            <input
              type="number"
              class="form-control form-control-solid"
              formControlName="groundArea"
              min="0"
            />
          </div>

          <div class="row mb-10">
            <div class="col-md-6 mb-5">
              <label class="form-label fw-bold"
                >Ground Area Min (Square Meters)</label
              >
              <input
                type="number"
                class="form-control form-control-solid"
                formControlName="groundAreaMin"
                min="0"
              />
            </div>
            <div class="col-md-6 mb-5">
              <label class="form-label fw-bold"
                >Ground Area Max (Square Meters)</label
              >
              <input
                type="number"
                class="form-control form-control-solid"
                formControlName="groundAreaMax"
                min="0"
              />
            </div>
          </div>

          <div class="row mb-10">
            <div class="col-md-6 mb-5">
              <label class="form-label fw-bold"
                >Garden Area (Square Meters)</label
              >
              <input
                type="number"
                class="form-control form-control-solid"
                formControlName="gardenArea"
                min="0"
              />
            </div>
            <div class="col-md-6 mb-5">
              <label class="form-label fw-bold"
                >Terrace Area (Square Meters)</label
              >
              <input
                type="number"
                class="form-control form-control-solid"
                formControlName="terraceArea"
                min="0"
              />
            </div>
          </div>

          <div class="row mb-10">
            <div class="col-md-6 mb-5">
              <label class="form-label fw-bold">Number of Rooms</label>
              <input
                type="number"
                class="form-control form-control-solid"
                formControlName="roomsCount"
                min="0"
              />
            </div>
            <div class="col-md-6 mb-5">
              <label class="form-label fw-bold">Number of Bathrooms</label>
              <input
                type="number"
                class="form-control form-control-solid"
                formControlName="bathroomsCount"
                min="0"
              />
            </div>
          </div>

          <div class="row mb-10">
            <div class="col-md-6 mb-5">
              <label class="form-label fw-bold">Number of Floors</label>
              <input
                type="number"
                class="form-control form-control-solid"
                formControlName="floorsCount"
                min="0"
              />
            </div>
            <div class="col-md-6 mb-5">
              <!-- Empty column for spacing -->
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">Floor</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button"
                id="floorDropdown"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <span>{{
                  getText(floorTypes, step3Form.get("floor")?.value) ||
                    "Select Floor"
                }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="floorDropdown">
                <li *ngFor="let floor of floorTypes">
                  <a
                    class="dropdown-item"
                    (click)="select(step3Form, 'floor', floor.value)"
                    >{{ floor.key }}</a
                  >
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block"
              >Favorite Floor</label
            >
            <input
              type="text"
              class="form-control form-control-solid"
              formControlName="favoriteFloor"
              placeholder="Enter favorite floor"
            />
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block"
              >Building Deadline</label
            >
            <input
              type="date"
              class="form-control form-control-solid"
              formControlName="buildingDeadline"
            />
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block"
              >Building License</label
            >
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button"
                id="buildingLicenseDropdown"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <span>{{
                  getText(
                    buildingLicenseTypes,
                    step3Form.get("buildingLicense")?.value
                  ) || "Select Building License"
                }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul
                class="dropdown-menu w-100"
                aria-labelledby="buildingLicenseDropdown"
              >
                <li *ngFor="let license of buildingLicenseTypes">
                  <a
                    class="dropdown-item"
                    (click)="
                      select(step3Form, 'buildingLicense', license.value)
                    "
                    >{{ license.key }}</a
                  >
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block"
              >Rent Duration (Months)</label
            >
            <input
              type="number"
              class="form-control form-control-solid"
              formControlName="rentDuration"
              placeholder="Enter rent duration in months"
              min="1"
            />
          </div>

          <div class="row mb-10">
            <div class="col-md-6 mb-5">
              <label class="form-label fw-bold">Rent Date From</label>
              <input
                type="date"
                class="form-control form-control-solid"
                formControlName="rentDateMin"
              />
            </div>
            <div class="col-md-6 mb-5">
              <label class="form-label fw-bold">Rent Date To</label>
              <input
                type="date"
                class="form-control form-control-solid"
                formControlName="rentDateMax"
              />
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block"
              >Furnishing Status</label
            >
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button"
                id="furnishingStatusDropdown"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <span>{{
                  getText(
                    furnishingStatusTypes,
                    step3Form.get("furnishingStatus")?.value
                  ) || "Select Furnishing Status"
                }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul
                class="dropdown-menu w-100"
                aria-labelledby="furnishingStatusDropdown"
              >
                <li *ngFor="let furnishing of furnishingStatusTypes">
                  <a
                    class="dropdown-item"
                    (click)="
                      select(step3Form, 'furnishingStatus', furnishing.value)
                    "
                    >{{ furnishing.key }}</a
                  >
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block"
              >Delivery Status</label
            >
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button"
                id="deliveryStatusDropdown"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <span>{{
                  getText(
                    deliveryStatusTypes,
                    step3Form.get("deliveryStatus")?.value
                  ) || "Select Delivery Status"
                }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul
                class="dropdown-menu w-100"
                aria-labelledby="deliveryStatusDropdown"
              >
                <li *ngFor="let delivery of deliveryStatusTypes">
                  <a
                    class="dropdown-item"
                    (click)="
                      select(step3Form, 'deliveryStatus', delivery.value)
                    "
                    >{{ delivery.key }}</a
                  >
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block"
              >Financial Status</label
            >
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button"
                id="financialStatusDropdown"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <span>{{
                  getText(
                    financialStatusTypes,
                    step3Form.get("financialStatus")?.value
                  ) || "Select Financial Status"
                }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul
                class="dropdown-menu w-100"
                aria-labelledby="financialStatusDropdown"
              >
                <li *ngFor="let financial of financialStatusTypes">
                  <a
                    class="dropdown-item"
                    (click)="
                      select(step3Form, 'financialStatus', financial.value)
                    "
                    >{{ financial.key }}</a
                  >
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block"
              >Legal Status</label
            >
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button"
                id="legalStatusDropdown"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <span>{{
                  getText(
                    legalStatusTypes,
                    step3Form.get("legalStatus")?.value
                  ) || "Select Legal Status"
                }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul
                class="dropdown-menu w-100"
                aria-labelledby="legalStatusDropdown"
              >
                <li *ngFor="let legal of legalStatusTypes">
                  <a
                    class="dropdown-item"
                    (click)="select(step3Form, 'legalStatus', legal.value)"
                    >{{ legal.key }}</a
                  >
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block"
              >Fit Out Condition</label
            >
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button"
                id="fitOutConditionDropdown"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <span>{{
                  getText(
                    fitOutConditionTypes,
                    step3Form.get("fitOutCondition")?.value
                  ) || "Select Fit Out Condition"
                }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul
                class="dropdown-menu w-100"
                aria-labelledby="fitOutConditionDropdown"
              >
                <li *ngFor="let fitOut of fitOutConditionTypes">
                  <a
                    class="dropdown-item"
                    (click)="select(step3Form, 'fitOutCondition', fitOut.value)"
                    >{{ fitOut.key }}</a
                  >
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block"
              >Finishing Status</label
            >
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button"
                id="finishingStatusDropdown"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <span>{{
                  getText(
                    finishingStatusTypes,
                    step3Form.get("finishingStatus")?.value
                  ) || "Select Finishing Status"
                }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul
                class="dropdown-menu w-100"
                aria-labelledby="finishingStatusDropdown"
              >
                <li *ngFor="let finishing of finishingStatusTypes">
                  <a
                    class="dropdown-item"
                    (click)="
                      select(step3Form, 'finishingStatus', finishing.value)
                    "
                    >{{ finishing.key }}</a
                  >
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block"
              >Unit View</label
            >
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button"
                id="unitViewDropdown"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <span>{{
                  getText(unitViewTypes, step3Form.get("unitView")?.value) ||
                    "Select Unit View"
                }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul
                class="dropdown-menu w-100"
                aria-labelledby="unitViewDropdown"
              >
                <li *ngFor="let view of unitViewTypes">
                  <a
                    class="dropdown-item"
                    (click)="select(step3Form, 'unitView', view.value)"
                    >{{ view.key }}</a
                  >
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block"
              >Unit Facing</label
            >
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button"
                id="unitFacingDropdown"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <span>{{
                  getText(
                    unitFacingTypes,
                    step3Form.get("unitFacing")?.value
                  ) || "Select Unit Facing"
                }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul
                class="dropdown-menu w-100"
                aria-labelledby="unitFacingDropdown"
              >
                <li *ngFor="let facing of unitFacingTypes">
                  <a
                    class="dropdown-item"
                    (click)="select(step3Form, 'unitFacing', facing.value)"
                    >{{ facing.key }}</a
                  >
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block"
              >Additional Amenities</label
            >
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button"
                id="amenitiesDropdown"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <span>{{
                  getSelectedAmenitiesText() || "Select Amenities"
                }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul
                class="dropdown-menu w-100 p-3"
                aria-labelledby="amenitiesDropdown"
              >
                <!-- All The Above Are Suitable - Special handling -->
                <li class="mb-2">
                  <div class="form-check">
                    <input
                      class="form-check-input"
                      type="checkbox"
                      id="amenity_alltheabovearesuitable"
                      [checked]="areAllAmenitiesSelected()"
                      (change)="onAllAmenitiesChange($event)"
                    />
                    <label
                      class="form-check-label text-start"
                      for="amenity_alltheabovearesuitable"
                    >
                      All The Above Are Suitable
                    </label>
                  </div>
                </li>

                <hr class="my-2" />

                <!-- Individual amenities -->
                <li *ngFor="let amenity of amenitiesTypes" class="mb-2">
                  <div class="form-check">
                    <input
                      class="form-check-input"
                      type="checkbox"
                      [id]="'amenity_' + amenity.value"
                      [checked]="isAmenitySelected(amenity.value)"
                      (change)="toggleAmenity(amenity.value)"
                    />
                    <label
                      class="form-check-label text-start"
                      [for]="'amenity_' + amenity.value"
                    >
                      {{ amenity.key }}
                    </label>
                  </div>
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">Notes</label>
            <textarea
              class="form-control form-control-solid"
              formControlName="notes"
              rows="4"
              placeholder="Enter any additional notes or comments..."
            ></textarea>
          </div>
        </div>

        <!-- Step 4: Project Documents -->
        <div *ngIf="currentStep === 4" [formGroup]="step4Form">
          <!-- Project Documents Cards -->
          <div class="mb-10 upload-card-container">
            <!--    Upload image of main unit -->
            <div class="card mb-5 cursor-pointer">
              <label for="mainImage" class="card-body text-center py-3">
                <div class="upload-icon">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <span class="upload-text">
                  Upload image of main unit
                  <span
                    *ngIf="getFileCount('mainImage') > 0"
                    class="badge bg-success ms-2"
                  >
                    {{ getFileCount("mainImage") }}
                  </span>
                </span>
                <input
                  type="file"
                  id="mainImage"
                  class="d-none"
                  (change)="onFileChange($event, 'mainImage')"
                  multiple
                />
              </label>
            </div>

            <!-- Upload a photos to the gallery -->
            <div class="card mb-5 cursor-pointer">
              <label for="galleryImages" class="card-body text-center py-3">
                <div class="upload-icon">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <span class="upload-text">
                  Upload photos to the gallery
                  <span
                    *ngIf="getFileCount('galleryImages') > 0"
                    class="badge bg-success ms-2"
                  >
                    {{ getFileCount("galleryImages") }}
                  </span>
                </span>
                <input
                  type="file"
                  id="galleryImages"
                  class="d-none"
                  (change)="onFileChange($event, 'galleryImages')"
                  multiple
                />
              </label>
            </div>
            <!-- Project Videos -->
            <div class="card mb-5 cursor-pointer">
              <label for="video" class="card-body text-center py-3">
                <div class="upload-icon">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <span class="upload-text">
                  Upload project videos

                  <span
                    *ngIf="getFileCount('video') > 0"
                    class="badge bg-success ms-2"
                  >
                    {{ getFileCount("video") }}
                  </span>
                </span>
                <input
                  type="file"
                  id="video"
                  class="d-none"
                  (change)="onFileChange($event, 'video')"
                  accept="video/*"
                  multiple
                />
              </label>
            </div>

            <!-- Upload unit plan -->
            <div class="card mb-5 cursor-pointer">
              <label
                for="unitInMasterPlanImage"
                class="card-body text-center py-3"
              >
                <div class="upload-icon">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <span class="upload-text">
                  Upload unit plan
                  <span
                    *ngIf="getFileCount('unitInMasterPlanImage') > 0"
                    class="badge bg-success ms-2"
                  >
                    {{ getFileCount("unitInMasterPlanImage") }}
                  </span>
                </span>
                <input
                  type="file"
                  id="unitInMasterPlanImage"
                  class="d-none"
                  (change)="onFileChange($event, 'unitInMasterPlanImage')"
                  multiple
                />
              </label>
            </div>
          </div>
        </div>

        <!-- Step 5: Financial Information -->
        <div *ngIf="currentStep === 5" [formGroup]="step5Form">
          <div class="mb-10">
            <label class="form-label fw-bold">Monthly Rent Range</label>
            <div class="row">
              <div class="col-md-6 mb-5">
                <input
                  type="number"
                  class="form-control form-control-solid"
                  placeholder="From"
                  formControlName="monthlyRentFrom"
                  min="0"
                />
              </div>
              <div class="col-md-6 mb-5">
                <input
                  type="number"
                  class="form-control form-control-solid"
                  placeholder="To"
                  formControlName="monthlyRentTo"
                  min="0"
                />
              </div>
            </div>
          </div>

          <div class="mb-10">
            <div class="form-check form-check-custom form-check-solid">
              <input
                class="form-check-input"
                type="checkbox"
                formControlName="wantRealEstateAgentOffers"
                id="agentOffers"
              />
              <label class="form-check-label" for="agentOffers">
                I want offers from real estate agents
              </label>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold">Daily Rent Range</label>
            <div class="row">
              <div class="col-md-6 mb-5">
                <input
                  type="number"
                  class="form-control form-control-solid"
                  placeholder="From"
                  formControlName="dailyRentFrom"
                  min="0"
                />
              </div>
              <div class="col-md-6 mb-5">
                <input
                  type="number"
                  class="form-control form-control-solid"
                  placeholder="To"
                  formControlName="dailyRentTo"
                  min="0"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Navigation Buttons -->
        <div class="d-flex justify-content-center pt-10">
          <ng-container *ngIf="currentStep !== totalSteps">
            <button
              type="button"
              class="btn btn-lg btn-navy px-10 py-3 rounded-pill w-100"
              [disabled]="!isCurrentFormValid()"
              (click)="nextStep()"
            >
              <span class="indicator-label text-white">
                <ng-container *ngIf="currentStep === 1"
                  >Next - Location Information</ng-container
                >
                <ng-container *ngIf="currentStep === 2"
                  >Next - Unit Information</ng-container
                >
                <ng-container *ngIf="currentStep === 3"
                  >Next - Project Documents</ng-container
                >
                <ng-container *ngIf="currentStep === 4"
                  >Next - Financial Information</ng-container
                >
              </span>
            </button>
          </ng-container>

          <ng-container *ngIf="currentStep === totalSteps">
            <button
              type="button"
              class="btn btn-lg btn-navy px-10 py-3 rounded-pill w-100"
              [disabled]="!isCurrentFormValid()"
              (click)="submitForm()"
            >
              <span class="indicator-label text-white">Submit Request</span>
            </button>
          </ng-container>
        </div>
      </form>
    </div>
  </div>
</div>
